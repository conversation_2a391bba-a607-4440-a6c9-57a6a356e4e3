import sqlite3
from datetime import datetime

print("🚨 开始简单修复...")

# 修复lottery.db
try:
    conn = sqlite3.connect('data/lottery.db')
    cursor = conn.cursor()
    
    # 简单插入，只使用基本字段
    cursor.execute("""
        INSERT OR REPLACE INTO lottery_data 
        (issue, draw_date, hundreds, tens, units, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
    """, ('2025211', '2025-08-09', 8, 9, 7, datetime.now().isoformat()))
    
    cursor.execute("""
        INSERT OR REPLACE INTO lottery_data 
        (issue, draw_date, hundreds, tens, units, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
    """, ('2025212', '2025-08-10', 4, 5, 6, datetime.now().isoformat()))
    
    conn.commit()
    conn.close()
    print("✅ lottery.db修复完成")
    
except Exception as e:
    print(f"❌ lottery.db修复失败: {e}")

# 修复fucai3d.db
try:
    conn = sqlite3.connect('data/fucai3d.db')
    cursor = conn.cursor()
    
    # 插入开奖数据
    cursor.execute("""
        INSERT OR REPLACE INTO lottery_data 
        (issue, draw_date, hundreds, tens, units, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
    """, ('2025211', '2025-08-09', 8, 9, 7, datetime.now().isoformat()))
    
    cursor.execute("""
        INSERT OR REPLACE INTO lottery_data 
        (issue, draw_date, hundreds, tens, units, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
    """, ('2025212', '2025-08-10', 4, 5, 6, datetime.now().isoformat()))
    
    # 修复预测期号
    cursor.execute("UPDATE final_predictions SET issue = '2025213' WHERE issue != '2025213'")
    
    conn.commit()
    conn.close()
    print("✅ fucai3d.db修复完成")
    
except Exception as e:
    print(f"❌ fucai3d.db修复失败: {e}")

# 验证结果
try:
    conn = sqlite3.connect('data/lottery.db')
    cursor = conn.cursor()
    cursor.execute("SELECT issue, draw_date, hundreds, tens, units FROM lottery_data ORDER BY issue DESC LIMIT 3")
    results = cursor.fetchall()
    conn.close()
    
    print("📊 最新记录:")
    for r in results:
        print(f"  期号:{r[0]}, 日期:{r[1]}, 号码:{r[2]}{r[3]}{r[4]}")
    
    if results and results[0][0] == '2025212':
        print("🎉 修复成功！2025212期数据已更新")
        print("🚀 现在应该预测2025213期")
    else:
        print("⚠️  可能需要进一步检查")
        
except Exception as e:
    print(f"❌ 验证失败: {e}")
