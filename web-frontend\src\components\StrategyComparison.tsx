import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Select,
  Space,
  Tag,
  Progress,
  Tooltip,
  Alert,
  Row,
  Col,
  Statistic,
  Badge,
  message,
  Modal,
  Descriptions
} from 'antd';
import {
  SwapOutlined,
  TrophyOutlined,
  Bar<PERSON>hartOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { Column, Radar } from '@ant-design/plots';

const { Option } = Select;

// 数据接口定义
interface Strategy {
  strategy_id: string;
  strategy_name: string;
  strategy_type: string;
  description: string;
  is_active: boolean;
  created_date: string;
  last_used: string;
  performance_score: number;
}

interface StrategyPerformance {
  strategy_id: string;
  strategy_name: string;
  total_predictions: number;
  perfect_hit_rate: number;
  average_accuracy: number;
  average_confidence: number;
  stability_score: number;
  overall_score: number;
  risk_level: 'low' | 'medium' | 'high';
  recommendations: string[];
}

interface ComparisonResult {
  strategies: StrategyPerformance[];
  best_strategy: string;
  comparison_period: string;
  insights: {
    most_stable: string;
    highest_accuracy: string;
    best_confidence: string;
    recommendations: string[];
  };
}

const StrategyComparison: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [selectedStrategies, setSelectedStrategies] = useState<string[]>([]);
  const [comparisonResult, setComparisonResult] = useState<ComparisonResult | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedStrategy, setSelectedStrategy] = useState<StrategyPerformance | null>(null);
  const [comparePeriod, setComparePeriod] = useState(30);

  useEffect(() => {
    fetchStrategies();
  }, []);

  const fetchStrategies = async () => {
    try {
      // 模拟策略数据（实际应该从API获取）
      const mockStrategies: Strategy[] = [
        {
          strategy_id: 'strategy_001',
          strategy_name: '保守型策略',
          strategy_type: 'conservative',
          description: '基于历史频率的保守预测策略',
          is_active: true,
          created_date: '2025-01-01',
          last_used: '2025-08-11',
          performance_score: 0.72
        },
        {
          strategy_id: 'strategy_002',
          strategy_name: '激进型策略',
          strategy_type: 'aggressive',
          description: '基于机器学习的激进预测策略',
          is_active: true,
          created_date: '2025-02-01',
          last_used: '2025-08-10',
          performance_score: 0.68
        },
        {
          strategy_id: 'strategy_003',
          strategy_name: '平衡型策略',
          strategy_type: 'balanced',
          description: '结合多种算法的平衡策略',
          is_active: true,
          created_date: '2025-03-01',
          last_used: '2025-08-11',
          performance_score: 0.75
        },
        {
          strategy_id: 'strategy_004',
          strategy_name: 'SHAP智能策略',
          strategy_type: 'ai_enhanced',
          description: '基于SHAP分析的智能推荐策略',
          is_active: true,
          created_date: '2025-07-01',
          last_used: '2025-08-11',
          performance_score: 0.78
        }
      ];
      
      setStrategies(mockStrategies);
    } catch (error) {
      message.error('获取策略列表失败');
    }
  };

  const handleCompareStrategies = async () => {
    if (selectedStrategies.length < 2) {
      message.warning('请至少选择2个策略进行对比');
      return;
    }

    setLoading(true);
    try {
      // 直接使用模拟数据（API接口开发中）
      const mockResult: ComparisonResult = {
        strategies: selectedStrategies.map((id, index) => {
          const strategy = strategies.find(s => s.strategy_id === id);
          return {
            strategy_id: id,
            strategy_name: strategy?.strategy_name || '',
            total_predictions: 50 + Math.floor(Math.random() * 50),
            perfect_hit_rate: 0.05 + Math.random() * 0.15,
            average_accuracy: 0.3 + Math.random() * 0.3,
            average_confidence: 0.6 + Math.random() * 0.3,
            stability_score: 0.5 + Math.random() * 0.4,
            overall_score: 0.4 + Math.random() * 0.4,
            risk_level: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as 'low' | 'medium' | 'high',
            recommendations: [
              '建议增加训练数据量',
              '优化特征选择算法',
              '调整模型参数'
            ]
          };
        }),
        best_strategy: selectedStrategies[0],
        comparison_period: `${comparePeriod}天`,
        insights: {
          most_stable: selectedStrategies[0],
          highest_accuracy: selectedStrategies[1] || selectedStrategies[0],
          best_confidence: selectedStrategies[2] || selectedStrategies[0],
          recommendations: [
            '建议优先使用SHAP智能策略',
            '保守型策略适合风险厌恶用户',
            '可以考虑组合多种策略'
          ]
        }
      };

      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      setComparisonResult(mockResult);
      message.success('策略对比完成');
    } catch (error) {
      message.error('策略对比失败');
    } finally {
      setLoading(false);
    }
  };

  const showStrategyDetail = (strategy: StrategyPerformance) => {
    setSelectedStrategy(strategy);
    setDetailModalVisible(true);
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'green';
      case 'medium': return 'orange';
      case 'high': return 'red';
      default: return 'default';
    }
  };

  const getRiskText = (risk: string) => {
    switch (risk) {
      case 'low': return '低风险';
      case 'medium': return '中风险';
      case 'high': return '高风险';
      default: return '未知';
    }
  };

  // 策略列表表格列定义
  const strategyColumns = [
    {
      title: '策略名称',
      dataIndex: 'strategy_name',
      key: 'strategy_name',
      render: (text: string, record: Strategy) => (
        <Space>
          <span>{text}</span>
          {record.is_active && <Badge status="success" text="活跃" />}
        </Space>
      ),
    },
    {
      title: '策略类型',
      dataIndex: 'strategy_type',
      key: 'strategy_type',
      render: (type: string) => {
        const typeMap = {
          conservative: { text: '保守型', color: 'blue' },
          aggressive: { text: '激进型', color: 'red' },
          balanced: { text: '平衡型', color: 'green' },
          ai_enhanced: { text: 'AI增强', color: 'purple' }
        };
        const config = typeMap[type as keyof typeof typeMap] || { text: type, color: 'default' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '性能评分',
      dataIndex: 'performance_score',
      key: 'performance_score',
      render: (score: number) => (
        <Progress
          percent={Math.round(score * 100)}
          size="small"
          status={score > 0.7 ? 'success' : score > 0.5 ? 'normal' : 'exception'}
        />
      ),
    },
    {
      title: '最后使用',
      dataIndex: 'last_used',
      key: 'last_used',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: Strategy) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<InfoCircleOutlined />}
              onClick={() => {
                // 显示策略详情
                message.info('策略详情功能开发中');
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 对比结果表格列定义
  const comparisonColumns = [
    {
      title: '策略名称',
      dataIndex: 'strategy_name',
      key: 'strategy_name',
      render: (text: string, record: StrategyPerformance) => (
        <Space>
          <span>{text}</span>
          {record.strategy_id === comparisonResult?.best_strategy && (
            <TrophyOutlined style={{ color: '#faad14' }} />
          )}
        </Space>
      ),
    },
    {
      title: '预测次数',
      dataIndex: 'total_predictions',
      key: 'total_predictions',
    },
    {
      title: '完全命中率',
      dataIndex: 'perfect_hit_rate',
      key: 'perfect_hit_rate',
      render: (rate: number) => `${(rate * 100).toFixed(1)}%`,
      sorter: (a: StrategyPerformance, b: StrategyPerformance) => a.perfect_hit_rate - b.perfect_hit_rate,
    },
    {
      title: '平均准确率',
      dataIndex: 'average_accuracy',
      key: 'average_accuracy',
      render: (rate: number) => `${(rate * 100).toFixed(1)}%`,
      sorter: (a: StrategyPerformance, b: StrategyPerformance) => a.average_accuracy - b.average_accuracy,
    },
    {
      title: '稳定性',
      dataIndex: 'stability_score',
      key: 'stability_score',
      render: (score: number) => (
        <Progress
          percent={Math.round(score * 100)}
          size="small"
          status={score > 0.7 ? 'success' : score > 0.5 ? 'normal' : 'exception'}
        />
      ),
      sorter: (a: StrategyPerformance, b: StrategyPerformance) => a.stability_score - b.stability_score,
    },
    {
      title: '风险等级',
      dataIndex: 'risk_level',
      key: 'risk_level',
      render: (risk: string) => (
        <Tag color={getRiskColor(risk)}>{getRiskText(risk)}</Tag>
      ),
    },
    {
      title: '综合评分',
      dataIndex: 'overall_score',
      key: 'overall_score',
      render: (score: number) => `${(score * 100).toFixed(1)}`,
      sorter: (a: StrategyPerformance, b: StrategyPerformance) => a.overall_score - b.overall_score,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: StrategyPerformance) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => showStrategyDetail(record)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  // 雷达图配置
  const radarConfig = {
    data: comparisonResult?.strategies.flatMap(strategy => [
      { name: strategy.strategy_name, metric: '准确率', value: strategy.average_accuracy * 100 },
      { name: strategy.strategy_name, metric: '稳定性', value: strategy.stability_score * 100 },
      { name: strategy.strategy_name, metric: '置信度', value: strategy.average_confidence * 100 },
      { name: strategy.strategy_name, metric: '命中率', value: strategy.perfect_hit_rate * 100 }
    ]) || [],
    xField: 'metric',
    yField: 'value',
    seriesField: 'name',
    meta: {
      value: {
        alias: '评分',
        min: 0,
        max: 100,
      },
    },
    xAxis: {
      line: null,
      tickLine: null,
      grid: {
        line: {
          style: {
            lineDash: null,
          },
        },
      },
    },
    yAxis: {
      line: null,
      tickLine: null,
      grid: {
        line: {
          type: 'line',
          style: {
            lineDash: null,
          },
        },
        alternateColor: 'rgba(0, 0, 0, 0.04)',
      },
    },
    point: {
      size: 2,
    },
    area: {},
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 策略选择和对比控制 */}
      <Card title="策略对比工具" style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Select
              mode="multiple"
              placeholder="选择要对比的策略"
              value={selectedStrategies}
              onChange={setSelectedStrategies}
              style={{ width: '100%' }}
            >
              {strategies.map(strategy => (
                <Option key={strategy.strategy_id} value={strategy.strategy_id}>
                  {strategy.strategy_name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              value={comparePeriod}
              onChange={setComparePeriod}
              style={{ width: '100%' }}
            >
              <Option value={7}>最近7天</Option>
              <Option value={30}>最近30天</Option>
              <Option value={90}>最近90天</Option>
            </Select>
          </Col>
          <Col span={6}>
            <Space>
              <Button
                type="primary"
                icon={<SwapOutlined />}
                onClick={handleCompareStrategies}
                loading={loading}
                disabled={selectedStrategies.length < 2}
              >
                开始对比
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchStrategies}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 策略列表 */}
      <Card title="可用策略" style={{ marginBottom: 24 }}>
        <Table
          columns={strategyColumns}
          dataSource={strategies}
          rowKey="strategy_id"
          rowSelection={{
            selectedRowKeys: selectedStrategies,
            onChange: setSelectedStrategies,
            type: 'checkbox',
          }}
          pagination={false}
        />
      </Card>

      {/* 对比结果 */}
      {comparisonResult && (
        <>
          {/* 对比洞察 */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="最佳策略"
                  value={strategies.find(s => s.strategy_id === comparisonResult.best_strategy)?.strategy_name}
                  prefix={<TrophyOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="最稳定策略"
                  value={strategies.find(s => s.strategy_id === comparisonResult.insights.most_stable)?.strategy_name}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="最高准确率"
                  value={strategies.find(s => s.strategy_id === comparisonResult.insights.highest_accuracy)?.strategy_name}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="对比期间"
                  value={comparisonResult.comparison_period}
                />
              </Card>
            </Col>
          </Row>

          {/* 对比图表 */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={24}>
              <Card title="策略性能雷达图">
                <Radar {...radarConfig} height={400} />
              </Card>
            </Col>
          </Row>

          {/* 详细对比表格 */}
          <Card title="详细对比结果">
            <Table
              columns={comparisonColumns}
              dataSource={comparisonResult.strategies}
              rowKey="strategy_id"
              pagination={false}
            />
          </Card>

          {/* 优化建议 */}
          <Card title="优化建议" style={{ marginTop: 24 }}>
            <Alert
              message="策略优化建议"
              description={
                <ul>
                  {comparisonResult.insights.recommendations.map((rec, index) => (
                    <li key={index}>{rec}</li>
                  ))}
                </ul>
              }
              type="info"
              showIcon
            />
          </Card>
        </>
      )}

      {/* 策略详情模态框 */}
      <Modal
        title="策略详情"
        visible={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedStrategy && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="策略名称">{selectedStrategy.strategy_name}</Descriptions.Item>
            <Descriptions.Item label="预测次数">{selectedStrategy.total_predictions}</Descriptions.Item>
            <Descriptions.Item label="完全命中率">{(selectedStrategy.perfect_hit_rate * 100).toFixed(1)}%</Descriptions.Item>
            <Descriptions.Item label="平均准确率">{(selectedStrategy.average_accuracy * 100).toFixed(1)}%</Descriptions.Item>
            <Descriptions.Item label="平均置信度">{(selectedStrategy.average_confidence * 100).toFixed(1)}%</Descriptions.Item>
            <Descriptions.Item label="稳定性评分">{(selectedStrategy.stability_score * 100).toFixed(1)}</Descriptions.Item>
            <Descriptions.Item label="风险等级">
              <Tag color={getRiskColor(selectedStrategy.risk_level)}>
                {getRiskText(selectedStrategy.risk_level)}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="综合评分">{(selectedStrategy.overall_score * 100).toFixed(1)}</Descriptions.Item>
            <Descriptions.Item label="优化建议" span={2}>
              <ul>
                {selectedStrategy.recommendations.map((rec, index) => (
                  <li key={index}>{rec}</li>
                ))}
              </ul>
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default StrategyComparison;
