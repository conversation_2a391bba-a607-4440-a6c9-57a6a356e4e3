# 福彩3D系统启动标准流程 - 永久记忆

## ⚠️ 绝对不能忘记的启动方式

### 🔴 关键原则
1. **永远先查记录**: 使用serena memory和knowledge-graph查询历史记录
2. **永远看文档**: 检查README.md和相关配置文件  
3. **永远按标准**: 严格按照已验证的方式执行
4. **绝不盲目尝试**: 不允许瞎启动或随意修改命令

### 🚀 标准启动流程（必须严格遵循）

#### 第一步：启动后端服务
```bash
# 正确命令（唯一正确方式）
python src/web/app.py

# ❌ 错误方式（绝对不能用）
python -m src.web.app  # 这是错误的！
cd src/web && python app.py  # 这也是错误的！
```

#### 第二步：启动前端服务
```bash
# 正确命令
cd web-frontend && npm run dev

# 验证地址
http://127.0.0.1:3000
```

### 📊 服务端口配置（固定不变）
- **后端服务**: http://127.0.0.1:8000
- **前端界面**: http://127.0.0.1:3000
- **API文档**: http://127.0.0.1:8000/api/docs
- **WebSocket**: ws://127.0.0.1:8000/ws

### 🔧 启动验证标准
- **后端成功标志**: 看到 "福彩3D Web界面系统启动完成"
- **前端成功标志**: 看到 "VITE v7.1.1 ready in XXXms"
- **启动时间**: 后端~15秒，前端~300ms

### 🚫 绝对禁止的行为
1. 不查记录就开始执行
2. 使用错误的启动命令
3. 随意修改启动方式
4. 忽略历史经验和文档

### 📝 每次启动前必做检查
1. 查询serena memory: "启动 后端 前端"
2. 查看README.md启动部分
3. 确认使用正确命令
4. 按照标准流程执行

## 🎯 承诺
我承诺永远记住这个标准流程，绝不再盲目尝试或使用错误的启动方式。每次都会先查记录，再按标准执行。