#!/usr/bin/env python3
"""最终修复脚本 - 适配现有数据库结构"""

import sqlite3
import os
from datetime import datetime

def check_table_structure():
    """检查数据库表结构"""
    print("🔍 检查数据库表结构...")
    
    try:
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(lottery_data)")
        columns = cursor.fetchall()
        conn.close()
        
        print("📊 lottery.db表结构:")
        column_names = [col[1] for col in columns]
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        return column_names
        
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return []

def fix_with_correct_structure():
    """使用正确的表结构修复数据"""
    print("🚨 开始最终修复...")
    
    # 检查表结构
    columns = check_table_structure()
    
    if not columns:
        print("❌ 无法获取表结构")
        return False
    
    success_count = 0
    
    # 修复lottery.db
    try:
        print("📊 修复lottery.db...")
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        # 根据现有表结构插入数据
        if 'span_value' in columns:
            # 包含span_value列
            cursor.execute("""
                INSERT OR REPLACE INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, sum_value, span_value, form_type, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, ('2025211', '2025-08-09', 8, 9, 7, 24, 2, '组六', datetime.now().isoformat()))
            
            cursor.execute("""
                INSERT OR REPLACE INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, sum_value, span_value, form_type, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, ('2025212', '2025-08-10', 4, 5, 6, 15, 2, '组六', datetime.now().isoformat()))
        else:
            # 不包含span_value列，使用基本结构
            cursor.execute("""
                INSERT OR REPLACE INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, ('2025211', '2025-08-09', 8, 9, 7, datetime.now().isoformat()))
            
            cursor.execute("""
                INSERT OR REPLACE INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, ('2025212', '2025-08-10', 4, 5, 6, datetime.now().isoformat()))
        
        conn.commit()
        conn.close()
        success_count += 1
        print("✅ lottery.db修复完成")
        print("  ✅ 插入期号2025211: 897")
        print("  ✅ 插入期号2025212: 456")
        
    except Exception as e:
        print(f"❌ lottery.db修复失败: {e}")
    
    # 修复fucai3d.db
    try:
        print("📊 修复fucai3d.db...")
        conn = sqlite3.connect('data/fucai3d.db')
        cursor = conn.cursor()
        
        # 检查fucai3d.db的表结构
        cursor.execute("PRAGMA table_info(lottery_data)")
        fucai_columns = [col[1] for col in cursor.fetchall()]
        
        # 插入开奖数据
        if 'span_value' in fucai_columns:
            cursor.execute("""
                INSERT OR REPLACE INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, sum_value, span_value, form_type, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, ('2025211', '2025-08-09', 8, 9, 7, 24, 2, '组六', datetime.now().isoformat()))
            
            cursor.execute("""
                INSERT OR REPLACE INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, sum_value, span_value, form_type, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, ('2025212', '2025-08-10', 4, 5, 6, 15, 2, '组六', datetime.now().isoformat()))
        else:
            cursor.execute("""
                INSERT OR REPLACE INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, ('2025211', '2025-08-09', 8, 9, 7, datetime.now().isoformat()))
            
            cursor.execute("""
                INSERT OR REPLACE INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, ('2025212', '2025-08-10', 4, 5, 6, datetime.now().isoformat()))
        
        # 修复预测期号
        cursor.execute("UPDATE final_predictions SET issue = '2025213' WHERE issue != '2025213'")
        updated_rows = cursor.rowcount
        
        conn.commit()
        conn.close()
        success_count += 1
        print(f"✅ fucai3d.db修复完成，更新了{updated_rows}条预测记录")
        
    except Exception as e:
        print(f"❌ fucai3d.db修复失败: {e}")
    
    # 验证修复结果
    print("\n🔍 验证修复结果:")
    try:
        # 验证lottery.db
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        cursor.execute("SELECT issue, draw_date, hundreds, tens, units FROM lottery_data ORDER BY issue DESC LIMIT 3")
        results = cursor.fetchall()
        conn.close()
        
        print("📊 lottery.db最新记录:")
        for r in results:
            print(f"  期号:{r[0]}, 日期:{r[1]}, 号码:{r[2]}{r[3]}{r[4]}")
        
        # 检查是否包含2025212期
        latest_issue = results[0][0] if results else "未知"
        if latest_issue == '2025212':
            print("✅ 2025212期数据已成功更新")
        else:
            print(f"⚠️  最新期号为{latest_issue}，可能需要进一步检查")
        
        # 验证fucai3d.db
        conn = sqlite3.connect('data/fucai3d.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT DISTINCT issue FROM final_predictions ORDER BY issue DESC LIMIT 1")
        result = cursor.fetchone()
        if result:
            pred_issue = result[0]
            print(f"🎯 当前预测期号: {pred_issue}")
            if pred_issue == '2025213':
                print("✅ 预测期号已修正为2025213期")
            else:
                print(f"⚠️  预测期号为{pred_issue}，可能需要进一步修正")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
    
    if success_count == 2:
        print("\n🎉 最终修复成功完成！")
        print("✅ 2025212期数据已更新 (开奖号码: 456)")
        print("✅ 预测期号已修正为: 2025213期")
        print("✅ 数据库同步完成")
        print("🚀 现在可以正确预测2025213期号码了！")
        return True
    else:
        print(f"\n⚠️  部分修复失败，成功{success_count}/2个数据库")
        return False

if __name__ == "__main__":
    fix_with_correct_structure()
