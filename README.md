# 福彩3D智能预测系统

[![Python Version](https://img.shields.io/badge/python-3.11.9-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com)

一个基于机器学习和深度学习技术的福彩3D号码预测系统，采用闭环设计理念，实现从数据采集到预测分析的全流程自动化。

## 🎯 项目特色

- **🔄 闭环系统设计**：数据采集→特征工程→模型训练→预测生成→结果评估→系统优化
- **🤖 多模型集成**：XGBoost + LightGBM + LSTM + Transformer 集成预测
- **📊 可解释性分析**：基于SHAP的预测结果解释和特征重要性分析
- **⚡ 实时预测**：自动化数据采集和实时预测生成
- **🔍 智能复盘**：基于真实历史数据的自动复盘和准确率分析
- **📈 性能监控**：完整的系统监控和自动优化机制
- **🌐 Web界面**：直观的预测结果展示和历史分析

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────┐
│                    闭环预测系统                          │
├─────────────────────────────────────────────────────────┤
│  数据采集 → 特征工程 → 模型训练 → 预测生成 → 结果评估    │
│     ↑                                           ↓       │
│  系统优化 ← 性能分析 ← 反馈收集 ← 预测验证 ← 结果输出    │
└─────────────────────────────────────────────────────────┘
```

## 🛠️ 技术栈

### 核心技术
- **编程语言**: Python 3.11.9
- **机器学习**: Scikit-learn, XGBoost, LightGBM
- **深度学习**: TensorFlow, PyTorch
- **时序分析**: Hugging Face Transformers, TimeMixer
- **可解释性**: SHAP, ShapTime
- **Web框架**: Flask/FastAPI
- **数据库**: SQLite/PostgreSQL
- **任务调度**: APScheduler

### 数据处理
- **数据采集**: Requests, BeautifulSoup
- **数据处理**: Pandas, NumPy
- **可视化**: Matplotlib, Plotly
- **特征工程**: 自动化特征生成和选择

## 📋 系统要求

### 最低配置
- **操作系统**: Windows 10/Linux/macOS
- **CPU**: Intel i5 或同等级别
- **内存**: 8GB RAM
- **存储**: 10GB 可用空间
- **Python**: 3.11.9

### 推荐配置
- **CPU**: Intel i7 或同等级别
- **内存**: 16GB RAM
- **GPU**: RTX 3060 或同等级别 (可选)
- **存储**: 50GB SSD空间

## 🚀 快速启动

### 系统端口配置
- **后端服务**: http://127.0.0.1:8000
- **前端界面**: http://127.0.0.1:3000
- **API文档**: http://127.0.0.1:8000/api/docs
- **WebSocket**: ws://127.0.0.1:8000/ws

### 启动步骤

#### 🚫 错误启动方式（已被禁止）
```bash
# ❌ 绝对禁止使用以下方式
python -m src.web.app          # 已被系统拦截
cd src/web && python app.py    # 工作目录错误
```

#### ✅ 方式一：标准启动脚本（推荐）
```bash
# 进入项目根目录
cd fucai3d

# 使用标准启动脚本（自动启动前后端）
python start_system.py
```

#### ✅ 方式二：手动启动

##### 1. 后端启动（必须先启动）

```bash
# 进入项目根目录
cd fucai3d

# 安装Python依赖（首次运行）
pip install -r requirements.txt

# 启动后端服务（绑定127.0.0.1:8000）
python src/web/app.py
```

**验证后端启动成功**：
- 浏览器访问 http://127.0.0.1:8000/health
- 看到 `{"status": "healthy"}` 表示启动成功

#### 2. 前端启动（后端启动后）

```bash
# 进入前端目录
cd web-frontend

# 安装依赖（首次运行）
npm install

# 启动前端开发服务器（绑定127.0.0.1:3000）
npm run dev
```

**验证前端启动成功**：
- 浏览器访问 http://127.0.0.1:3000
- 看到福彩3D预测系统界面

### 🔧 端口冲突解决

#### 后端端口8000被占用
```bash
# 查找占用进程
netstat -ano | findstr :8000
# 或使用 PowerShell
Get-NetTCPConnection -LocalPort 8000

# 终止占用进程（替换PID为实际进程ID）
taskkill /PID <PID> /F

# 重新启动后端
python src/web/app.py
```

#### 前端端口3000被占用
```bash
# 查找占用进程
netstat -ano | findstr :3000
# 或使用 PowerShell
Get-NetTCPConnection -LocalPort 3000

# 终止占用进程（替换PID为实际进程ID）
taskkill /PID <PID> /F

# 重新启动前端
npm run dev
```

#### 强制重启所有端口
```bash
# Windows PowerShell 一键重启脚本
# 终止可能占用端口的进程
Get-Process | Where-Object {$_.ProcessName -like "*node*" -or $_.ProcessName -like "*python*"} | Stop-Process -Force

# 等待2秒
Start-Sleep -Seconds 2

# 重新启动服务
# 先启动后端
Start-Process -FilePath "python" -ArgumentList "src/web/app.py" -WorkingDirectory "."

# 等待后端启动
Start-Sleep -Seconds 5

# 再启动前端
Start-Process -FilePath "npm" -ArgumentList "run", "dev" -WorkingDirectory "web-frontend"
```

## 🛠️ 开发环境配置

### 1. 环境准备

```bash
# 克隆项目
git clone https://github.com/your-username/fucai3d-prediction.git
cd fucai3d-prediction

# 创建虚拟环境
python -m venv fucai3d_env

# 激活虚拟环境
# Windows
fucai3d_env\Scripts\activate
# Linux/macOS
source fucai3d_env/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据库初始化

```bash
# 数据库文件会在首次启动时自动创建
# 历史开奖数据已包含在 data/lottery.db 中（8359条真实记录）

# 如需重新初始化数据库
python scripts/init_database.py

# 如需更新历史数据
python scripts/collect_historical_data.py
```

### 3. 模型训练

```bash
# 特征工程
python scripts/feature_engineering.py

# 训练基础模型
python scripts/train_base_models.py

# 训练深度学习模型
python scripts/train_deep_models.py

# 模型集成优化
python scripts/ensemble_optimization.py
```

### 4. 启动服务

```bash
# 启动Web服务
python app.py

# 启动定时任务
python scheduler.py
```

访问 http://localhost:5000 查看预测结果

## 📁 项目结构

```
fucai3d-prediction/
├── app.py                      # Web应用主程序
├── scheduler.py                # 定时任务调度器
├── requirements.txt            # 依赖包列表
├── config/                     # 配置文件
│   ├── settings.py            # 系统配置
│   └── model_config.py        # 模型配置
├── src/                       # 源代码
│   ├── data/                  # 数据处理模块
│   │   ├── collector.py       # 数据采集
│   │   ├── processor.py       # 数据预处理
│   │   └── feature_engine.py  # 特征工程
│   ├── models/                # 模型模块
│   │   ├── base_models.py     # 基础模型
│   │   ├── deep_models.py     # 深度学习模型
│   │   └── ensemble.py        # 集成模型
│   ├── prediction/            # 预测模块
│   │   ├── engine.py          # 预测引擎
│   │   └── evaluator.py       # 结果评估
│   ├── utils/                 # 工具模块
│   │   ├── database.py        # 数据库操作
│   │   ├── logger.py          # 日志管理
│   │   └── monitor.py         # 系统监控
│   └── web/                   # Web界面
│       ├── routes.py          # 路由定义
│       └── templates/         # 页面模板
├── data/                      # 数据目录
│   ├── raw/                   # 原始数据
│   ├── processed/             # 处理后数据
│   └── models/                # 训练好的模型
├── logs/                      # 日志文件
├── tests/                     # 测试代码
├── scripts/                   # 脚本工具
└── docs/                      # 文档
```

## 🎮 使用指南

### 数据采集

系统支持多数据源采集，主要数据源为：
- **主数据源**: https://www.17500.cn/chart/3d-tjb.html
- **备用数据源**: 多个备用网站确保数据获取稳定性

```python
from src.data.collector import DataCollector

collector = DataCollector()
# 采集最新数据
latest_data = collector.collect_latest()
# 采集历史数据
historical_data = collector.collect_historical(start_date='2020-01-01')
```

### 特征工程

系统提供多层次特征工程：

```python
from src.data.feature_engine import FeatureEngine

fe = FeatureEngine()
# 基础特征提取
basic_features = fe.extract_basic_features(data)
# 统计特征生成
stat_features = fe.generate_statistical_features(data)
# 高级特征构建
advanced_features = fe.build_advanced_features(data)
```

### 模型训练

支持多种模型训练：

```python
from src.models.ensemble import EnsembleModel

# 创建集成模型
ensemble = EnsembleModel()
# 训练模型
ensemble.train(X_train, y_train)
# 模型评估
scores = ensemble.evaluate(X_test, y_test)
```

### 预测生成

```python
from src.prediction.engine import PredictionEngine

engine = PredictionEngine()
# 生成预测
prediction = engine.predict_next_period()
print(f"预测号码: {prediction['numbers']}")
print(f"置信度: {prediction['confidence']:.2f}")
```

## 📊 性能指标

### 当前性能表现
- **完全命中率**: 12-18%
- **位置准确率**: 35-45%
- **和值误差**: ±1.5以内
- **系统稳定性**: >95%
- **响应时间**: <2秒

### 预期目标
- **短期目标** (3个月): 完全命中率达到15%
- **中期目标** (6个月): 位置准确率达到45%
- **长期目标** (1年): 完全命中率达到20%

## 🔧 配置说明

### 基础配置 (config/settings.py)

```python
# 数据源配置
DATA_SOURCES = {
    'primary': 'https://www.17500.cn/chart/3d-tjb.html',
    'backup': ['backup_url_1', 'backup_url_2']
}

# 数据库配置
DATABASE_CONFIG = {
    'type': 'sqlite',  # 或 'postgresql'
    'path': 'data/lottery.db',
    'echo': False
}

# 模型配置
MODEL_CONFIG = {
    'ensemble_weights': {
        'xgboost': 0.3,
        'lightgbm': 0.3,
        'lstm': 0.2,
        'transformer': 0.2
    }
}
```

### 模型参数配置 (config/model_config.py)

```python
# XGBoost参数
XGB_PARAMS = {
    'n_estimators': 200,
    'max_depth': 6,
    'learning_rate': 0.1,
    'subsample': 0.8,
    'colsample_bytree': 0.8,
    'random_state': 42
}

# LSTM参数
LSTM_PARAMS = {
    'sequence_length': 30,
    'hidden_units': 64,
    'layers': 2,
    'dropout': 0.2,
    'batch_size': 32
}
```

## 🐳 Docker部署

### 构建镜像

```bash
# 构建Docker镜像
docker build -t fucai3d-prediction .

# 运行容器
docker run -d -p 5000:5000 --name fucai3d-app fucai3d-prediction
```

### Docker Compose部署

```bash
# 使用docker-compose部署
docker-compose up -d
```

## 📈 监控与维护

### 系统监控

系统提供完整的监控功能：
- **预测准确率监控**
- **系统性能监控**
- **数据质量监控**
- **模型性能监控**

### 日志管理

```bash
# 查看应用日志
tail -f logs/app.log

# 查看预测日志
tail -f logs/prediction.log

# 查看错误日志
tail -f logs/error.log
```

### 模型更新

系统支持自动模型更新：
- **定期重训练**: 每周自动重训练模型
- **性能监控**: 实时监控模型性能下降
- **自动回滚**: 新模型性能不佳时自动回滚

## ⚠️ 免责声明

本系统仅供学习研究和技术交流使用，预测结果仅供参考，不构成任何投注建议。彩票具有随机性，任何预测都无法保证准确性。请理性对待预测结果，合理控制风险。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献指南

欢迎贡献代码！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何参与项目开发。

## 📞 联系方式

- **项目维护者**: [Your Name]
- **邮箱**: <EMAIL>
- **GitHub**: https://github.com/your-username/fucai3d-prediction

## 🙏 致谢

感谢以下开源项目的支持：
- [Scikit-learn](https://scikit-learn.org/)
- [XGBoost](https://xgboost.readthedocs.io/)
- [TensorFlow](https://tensorflow.org/)
- [Flask](https://flask.palletsprojects.com/)
- [SHAP](https://shap.readthedocs.io/)

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
