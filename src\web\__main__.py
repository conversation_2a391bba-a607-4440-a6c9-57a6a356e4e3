#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误启动方式拦截器
防止使用 python -m src.web.app 启动
"""

import sys
import os

def block_wrong_startup():
    """阻止错误的启动方式"""
    print("🚫 错误的启动方式被拦截！")
    print("=" * 60)
    print("❌ 您使用了: python -m src.web.app")
    print("❌ 这是错误的启动方式！")
    print("")
    print("✅ 正确的启动方式:")
    print("   1. 进入项目根目录")
    print("   2. 运行: python src/web/app.py")
    print("")
    print("📖 详细说明:")
    print("   - 查看 README.md 文件")
    print("   - 查看 serena memory 中的启动配置")
    print("   - 使用标准启动脚本: python start_system.py")
    print("")
    print("🔧 端口配置:")
    print("   - 后端: http://127.0.0.1:8000")
    print("   - 前端: http://127.0.0.1:3000")
    print("")
    print("⚠️  为了系统稳定性，已禁止此启动方式")
    print("=" * 60)
    
    # 强制退出
    sys.exit(1)

if __name__ == "__main__":
    block_wrong_startup()
