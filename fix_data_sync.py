#!/usr/bin/env python3
"""
紧急数据同步修复脚本
解决2025212期数据未同步的问题
"""

import sqlite3
import requests
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fetch_latest_data():
    """从数据源获取最新数据"""
    try:
        url = "https://data.17500.cn/3d_asc.txt"
        logger.info(f"正在获取数据源: {url}")
        
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        lines = response.text.strip().split('\n')
        logger.info(f"获取到 {len(lines)} 行数据")
        
        # 解析最新的几条数据
        latest_records = []
        for line in lines[-10:]:  # 取最后10条
            parts = line.split()
            if len(parts) >= 4:
                issue = parts[0]
                date = parts[1]
                hundreds = int(parts[2])
                tens = int(parts[3])
                units = int(parts[4])
                
                latest_records.append({
                    'issue': issue,
                    'draw_date': date,
                    'hundreds': hundreds,
                    'tens': tens,
                    'units': units
                })
        
        return latest_records
        
    except Exception as e:
        logger.error(f"获取数据失败: {e}")
        return []

def update_database(records):
    """更新数据库"""
    try:
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        # 检查当前最新期号
        cursor.execute("SELECT MAX(issue) FROM lottery_data")
        result = cursor.fetchone()
        current_latest = result[0] if result and result[0] else "0"
        
        logger.info(f"数据库当前最新期号: {current_latest}")
        
        updated_count = 0
        for record in records:
            # 检查是否已存在
            cursor.execute("SELECT COUNT(*) FROM lottery_data WHERE issue = ?", (record['issue'],))
            exists = cursor.fetchone()[0] > 0
            
            if not exists:
                # 插入新记录
                cursor.execute("""
                    INSERT INTO lottery_data (
                        issue, draw_date, hundreds, tens, units,
                        sum_value, span_value, form_type, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record['issue'],
                    record['draw_date'],
                    record['hundreds'],
                    record['tens'],
                    record['units'],
                    record['hundreds'] + record['tens'] + record['units'],  # sum_value
                    max(record['hundreds'], record['tens'], record['units']) - min(record['hundreds'], record['tens'], record['units']),  # span_value
                    '组六' if len(set([record['hundreds'], record['tens'], record['units']])) == 3 else '组三',  # form_type
                    datetime.now().isoformat()
                ))
                updated_count += 1
                logger.info(f"插入新记录: 期号{record['issue']}, 号码{record['hundreds']}{record['tens']}{record['units']}")
            else:
                logger.info(f"记录已存在: 期号{record['issue']}")
        
        conn.commit()
        conn.close()
        
        logger.info(f"数据库更新完成，新增 {updated_count} 条记录")
        return updated_count
        
    except Exception as e:
        logger.error(f"数据库更新失败: {e}")
        return 0

def sync_to_fucai3d_db():
    """同步数据到fucai3d.db"""
    try:
        # 从lottery.db获取最新数据
        source_conn = sqlite3.connect('data/lottery.db')
        source_cursor = source_conn.cursor()
        
        source_cursor.execute("""
            SELECT issue, draw_date, hundreds, tens, units 
            FROM lottery_data 
            ORDER BY draw_date DESC, issue DESC 
            LIMIT 10
        """)
        source_data = source_cursor.fetchall()
        source_conn.close()
        
        if not source_data:
            logger.warning("源数据库没有数据")
            return False
        
        # 更新fucai3d.db
        target_conn = sqlite3.connect('data/fucai3d.db')
        target_cursor = target_conn.cursor()
        
        # 确保表存在
        target_cursor.execute("""
            CREATE TABLE IF NOT EXISTS lottery_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                issue TEXT UNIQUE,
                draw_date TEXT,
                hundreds INTEGER,
                tens INTEGER,
                units INTEGER,
                sum_value INTEGER,
                span_value INTEGER,
                form_type TEXT,
                created_at TEXT
            )
        """)
        
        synced_count = 0
        for record in source_data:
            issue, draw_date, hundreds, tens, units = record
            
            # 检查是否已存在
            target_cursor.execute("SELECT COUNT(*) FROM lottery_data WHERE issue = ?", (issue,))
            exists = target_cursor.fetchone()[0] > 0
            
            if not exists:
                target_cursor.execute("""
                    INSERT INTO lottery_data (
                        issue, draw_date, hundreds, tens, units,
                        sum_value, span_value, form_type, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    issue, draw_date, hundreds, tens, units,
                    hundreds + tens + units,
                    max(hundreds, tens, units) - min(hundreds, tens, units),
                    '组六' if len(set([hundreds, tens, units])) == 3 else '组三',
                    datetime.now().isoformat()
                ))
                synced_count += 1
                logger.info(f"同步到fucai3d.db: 期号{issue}, 号码{hundreds}{tens}{units}")
        
        target_conn.commit()
        target_conn.close()
        
        logger.info(f"同步到fucai3d.db完成，新增 {synced_count} 条记录")
        return True
        
    except Exception as e:
        logger.error(f"同步到fucai3d.db失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始紧急数据同步修复")
    
    # 1. 获取最新数据
    logger.info("📥 步骤1: 获取最新数据")
    latest_records = fetch_latest_data()
    
    if not latest_records:
        logger.error("❌ 获取数据失败，退出")
        return False
    
    # 显示最新数据
    logger.info("📊 最新数据:")
    for record in latest_records[-3:]:
        logger.info(f"  期号:{record['issue']}, 日期:{record['draw_date']}, 号码:{record['hundreds']}{record['tens']}{record['units']}")
    
    # 2. 更新主数据库
    logger.info("📤 步骤2: 更新主数据库")
    updated_count = update_database(latest_records)
    
    # 3. 同步到预测数据库
    logger.info("🔄 步骤3: 同步到预测数据库")
    sync_success = sync_to_fucai3d_db()
    
    # 4. 验证结果
    logger.info("✅ 步骤4: 验证结果")
    try:
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        cursor.execute("SELECT issue, draw_date, hundreds, tens, units FROM lottery_data ORDER BY draw_date DESC, issue DESC LIMIT 3")
        results = cursor.fetchall()
        conn.close()
        
        logger.info("🎯 lottery.db最新3条记录:")
        for r in results:
            logger.info(f"  期号:{r[0]}, 日期:{r[1]}, 号码:{r[2]}{r[3]}{r[4]}")
    except Exception as e:
        logger.error(f"验证失败: {e}")
    
    logger.info("🎉 数据同步修复完成")
    return True

if __name__ == "__main__":
    main()
