import sqlite3
from datetime import datetime

# 更新lottery.db
print("更新lottery.db...")
conn = sqlite3.connect('data/lottery.db')
cursor = conn.cursor()

# 插入2025211期数据
cursor.execute("""
    INSERT OR REPLACE INTO lottery_data (
        issue, draw_date, hundreds, tens, units, sum_value, span_value, form_type, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
""", ('2025211', '2025-08-09', 8, 9, 7, 24, 2, '组六', datetime.now().isoformat()))

# 插入2025212期数据
cursor.execute("""
    INSERT OR REPLACE INTO lottery_data (
        issue, draw_date, hundreds, tens, units, sum_value, span_value, form_type, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
""", ('2025212', '2025-08-10', 4, 5, 6, 15, 2, '组六', datetime.now().isoformat()))

conn.commit()
conn.close()
print("lottery.db更新完成")

# 更新fucai3d.db
print("更新fucai3d.db...")
conn = sqlite3.connect('data/fucai3d.db')
cursor = conn.cursor()

# 确保表存在
cursor.execute("""
    CREATE TABLE IF NOT EXISTS lottery_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        issue TEXT UNIQUE,
        draw_date TEXT,
        hundreds INTEGER,
        tens INTEGER,
        units INTEGER,
        sum_value INTEGER,
        span_value INTEGER,
        form_type TEXT,
        created_at TEXT
    )
""")

# 插入或更新数据
cursor.execute("""
    INSERT OR REPLACE INTO lottery_data (
        issue, draw_date, hundreds, tens, units, sum_value, span_value, form_type, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
""", ('2025211', '2025-08-09', 8, 9, 7, 24, 2, '组六', datetime.now().isoformat()))

cursor.execute("""
    INSERT OR REPLACE INTO lottery_data (
        issue, draw_date, hundreds, tens, units, sum_value, span_value, form_type, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
""", ('2025212', '2025-08-10', 4, 5, 6, 15, 2, '组六', datetime.now().isoformat()))

# 修复预测期号
cursor.execute("UPDATE final_predictions SET issue = '2025213' WHERE issue > '2025213'")

conn.commit()
conn.close()
print("fucai3d.db更新完成")

# 验证结果
print("\n验证结果:")
conn = sqlite3.connect('data/lottery.db')
cursor = conn.cursor()
cursor.execute("SELECT issue, draw_date, hundreds, tens, units FROM lottery_data ORDER BY draw_date DESC, issue DESC LIMIT 3")
results = cursor.fetchall()
conn.close()

print("lottery.db最新3条记录:")
for r in results:
    print(f"  期号:{r[0]}, 日期:{r[1]}, 号码:{r[2]}{r[3]}{r[4]}")

conn = sqlite3.connect('data/fucai3d.db')
cursor = conn.cursor()
cursor.execute("SELECT DISTINCT issue FROM final_predictions ORDER BY issue DESC LIMIT 1")
result = cursor.fetchone()
if result:
    print(f"fucai3d.db当前预测期号: {result[0]}")
conn.close()

print("\n✅ 数据更新完成！现在应该预测2025213期")
