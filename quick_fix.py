import sqlite3
import os
from datetime import datetime

print("🚀 开始紧急数据修复...")

# 确保数据目录存在
if not os.path.exists('data'):
    os.makedirs('data')
    print("📁 创建data目录")

# 更新lottery.db
print("📊 更新lottery.db...")
try:
    conn = sqlite3.connect('data/lottery.db')
    cursor = conn.cursor()

    # 检查表是否存在
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS lottery_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            issue TEXT UNIQUE,
            draw_date TEXT,
            hundreds INTEGER,
            tens INTEGER,
            units INTEGER,
            sum_value INTEGER,
            span_value INTEGER,
            form_type TEXT,
            created_at TEXT
        )
    """)

    # 插入2025211期数据
    cursor.execute("""
        INSERT OR REPLACE INTO lottery_data (
            issue, draw_date, hundreds, tens, units, sum_value, span_value, form_type, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, ('2025211', '2025-08-09', 8, 9, 7, 24, 2, '组六', datetime.now().isoformat()))

    # 插入2025212期数据
    cursor.execute("""
        INSERT OR REPLACE INTO lottery_data (
            issue, draw_date, hundreds, tens, units, sum_value, span_value, form_type, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, ('2025212', '2025-08-10', 4, 5, 6, 15, 2, '组六', datetime.now().isoformat()))

    conn.commit()
    conn.close()
    print("✅ lottery.db更新完成")
except Exception as e:
    print(f"❌ lottery.db更新失败: {e}")

# 更新fucai3d.db
print("📊 更新fucai3d.db...")
try:
    conn = sqlite3.connect('data/fucai3d.db')
    cursor = conn.cursor()

    # 确保表存在
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS lottery_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            issue TEXT UNIQUE,
            draw_date TEXT,
            hundreds INTEGER,
            tens INTEGER,
            units INTEGER,
            sum_value INTEGER,
            span_value INTEGER,
            form_type TEXT,
            created_at TEXT
        )
    """)

    # 插入或更新数据
    cursor.execute("""
        INSERT OR REPLACE INTO lottery_data (
            issue, draw_date, hundreds, tens, units, sum_value, span_value, form_type, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, ('2025211', '2025-08-09', 8, 9, 7, 24, 2, '组六', datetime.now().isoformat()))

    cursor.execute("""
        INSERT OR REPLACE INTO lottery_data (
            issue, draw_date, hundreds, tens, units, sum_value, span_value, form_type, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, ('2025212', '2025-08-10', 4, 5, 6, 15, 2, '组六', datetime.now().isoformat()))

    # 检查并创建final_predictions表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS final_predictions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            issue TEXT,
            hundreds INTEGER,
            tens INTEGER,
            units INTEGER,
            created_at TEXT
        )
    """)

    # 修复预测期号
    cursor.execute("UPDATE final_predictions SET issue = '2025213' WHERE issue > '2025213'")
    updated_rows = cursor.rowcount

    conn.commit()
    conn.close()
    print(f"✅ fucai3d.db更新完成，修复了{updated_rows}条预测记录")
except Exception as e:
    print(f"❌ fucai3d.db更新失败: {e}")

# 验证结果
print("\n🔍 验证修复结果:")
try:
    # 验证lottery.db
    conn = sqlite3.connect('data/lottery.db')
    cursor = conn.cursor()
    cursor.execute("SELECT issue, draw_date, hundreds, tens, units FROM lottery_data ORDER BY draw_date DESC, issue DESC LIMIT 3")
    results = cursor.fetchall()
    conn.close()

    print("📊 lottery.db最新3条记录:")
    for r in results:
        print(f"  期号:{r[0]}, 日期:{r[1]}, 号码:{r[2]}{r[3]}{r[4]}")

    # 验证fucai3d.db
    conn = sqlite3.connect('data/fucai3d.db')
    cursor = conn.cursor()

    # 检查预测期号
    cursor.execute("SELECT DISTINCT issue FROM final_predictions ORDER BY issue DESC LIMIT 1")
    result = cursor.fetchone()
    if result:
        print(f"🎯 fucai3d.db当前预测期号: {result[0]}")
    else:
        print("⚠️  fucai3d.db中没有预测记录")

    # 检查最新开奖数据
    cursor.execute("SELECT issue, hundreds, tens, units FROM lottery_data ORDER BY issue DESC LIMIT 2")
    lottery_results = cursor.fetchall()
    print("📊 fucai3d.db最新开奖记录:")
    for r in lottery_results:
        print(f"  期号:{r[0]}, 号码:{r[1]}{r[2]}{r[3]}")

    conn.close()

    print("\n🎉 数据修复完成！")
    print("✅ 2025212期开奖数据已更新 (456)")
    print("✅ 预测期号已修正为2025213期")
    print("✅ 数据库同步完成")

except Exception as e:
    print(f"❌ 验证失败: {e}")

print("\n🚀 现在可以正确预测2025213期号码了！")
