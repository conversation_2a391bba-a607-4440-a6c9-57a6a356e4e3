import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Select,
  DatePicker,
  Button,
  Statistic,
  Spin,
  Alert,
  Space,
  Tabs,
  Radio,
  message
} from 'antd';
import {
  ReloadOutlined,
  DownloadOutlined,
  Bar<PERSON>hartOutlined,
  LineChartOutlined,
  <PERSON>MapOutlined,
  RadarChartOutlined
} from '@ant-design/icons';
import { Column, Line, Radar, Heatmap } from '@ant-design/plots';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

// 数据接口定义
interface PositionAccuracy {
  position: 'hundreds' | 'tens' | 'units';
  accuracy_rate: number;
  total_predictions: number;
  correct_predictions: number;
  time_period: string;
}

interface TimeSeriesAccuracy {
  date: string;
  accuracy_rate: number;
  position?: string;
  model_source?: string;
}

interface ModelPerformance {
  model_name: string;
  accuracy_rate: number;
  total_predictions: number;
  perfect_hits: number;
  confidence_score: number;
}

interface AccuracyHeatmapData {
  date: string;
  position: string;
  accuracy_rate: number;
  prediction_count: number;
}

interface AnalyticsData {
  position_accuracy: PositionAccuracy[];
  time_series: TimeSeriesAccuracy[];
  model_performance: ModelPerformance[];
  heatmap_data: AccuracyHeatmapData[];
  summary_stats: {
    overall_accuracy: number;
    total_predictions: number;
    best_position: string;
    best_model: string;
    trend_direction: 'improving' | 'declining' | 'stable';
  };
}

const AccuracyAnalytics: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // 筛选状态
  const [filters, setFilters] = useState({
    dateRange: [dayjs().subtract(30, 'day'), dayjs()] as [dayjs.Dayjs, dayjs.Dayjs],
    models: [] as string[],
    positions: ['hundreds', 'tens', 'units'] as string[],
    timeGranularity: 'daily' as 'daily' | 'weekly' | 'monthly'
  });

  useEffect(() => {
    fetchAnalyticsData();
  }, [filters]);

  const fetchAnalyticsData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams({
        startDate: filters.dateRange[0].format('YYYY-MM-DD'),
        endDate: filters.dateRange[1].format('YYYY-MM-DD'),
        models: filters.models.join(','),
        positions: filters.positions.join(','),
        granularity: filters.timeGranularity
      });

      const response = await fetch(`/api/prediction-review/analytics/detailed?${params}`);
      
      if (!response.ok) {
        throw new Error('获取分析数据失败');
      }

      const result = await response.json();
      
      if (result.status === 'success') {
        // 模拟数据结构转换（实际应该从API返回）
        const analyticsData: AnalyticsData = {
          position_accuracy: [
            { position: 'hundreds', accuracy_rate: 0.35, total_predictions: 100, correct_predictions: 35, time_period: '30天' },
            { position: 'tens', accuracy_rate: 0.42, total_predictions: 100, correct_predictions: 42, time_period: '30天' },
            { position: 'units', accuracy_rate: 0.38, total_predictions: 100, correct_predictions: 38, time_period: '30天' }
          ],
          time_series: generateTimeSeriesData(),
          model_performance: [
            { model_name: 'XGBoost', accuracy_rate: 0.45, total_predictions: 50, perfect_hits: 8, confidence_score: 0.78 },
            { model_name: 'LightGBM', accuracy_rate: 0.38, total_predictions: 30, perfect_hits: 5, confidence_score: 0.72 },
            { model_name: 'LSTM', accuracy_rate: 0.33, total_predictions: 20, perfect_hits: 3, confidence_score: 0.65 }
          ],
          heatmap_data: generateHeatmapData(),
          summary_stats: {
            overall_accuracy: 0.38,
            total_predictions: 100,
            best_position: 'tens',
            best_model: 'XGBoost',
            trend_direction: 'improving'
          }
        };
        
        setData(analyticsData);
      } else {
        throw new Error(result.message || '数据格式错误');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
      message.error('获取分析数据失败');
    } finally {
      setLoading(false);
    }
  };

  const generateTimeSeriesData = (): TimeSeriesAccuracy[] => {
    const data: TimeSeriesAccuracy[] = [];
    const startDate = filters.dateRange[0];
    const endDate = filters.dateRange[1];
    
    for (let date = startDate; date.isBefore(endDate); date = date.add(1, 'day')) {
      data.push({
        date: date.format('YYYY-MM-DD'),
        accuracy_rate: 0.3 + Math.random() * 0.4, // 模拟数据
        position: 'overall'
      });
    }
    
    return data;
  };

  const generateHeatmapData = (): AccuracyHeatmapData[] => {
    const data: AccuracyHeatmapData[] = [];
    const positions = ['hundreds', 'tens', 'units'];
    const startDate = filters.dateRange[0];
    
    for (let i = 0; i < 7; i++) {
      const date = startDate.add(i, 'day').format('YYYY-MM-DD');
      positions.forEach(position => {
        data.push({
          date,
          position,
          accuracy_rate: 0.2 + Math.random() * 0.6, // 模拟数据
          prediction_count: Math.floor(Math.random() * 10) + 1
        });
      });
    }
    
    return data;
  };

  const handleExport = () => {
    message.info('导出功能开发中');
  };

  // 位置准确率图表配置
  const positionChartConfig = {
    data: data?.position_accuracy.map(item => ({
      position: item.position === 'hundreds' ? '百位' : 
                item.position === 'tens' ? '十位' : '个位',
      accuracy: item.accuracy_rate * 100,
      count: item.total_predictions
    })) || [],
    xField: 'position',
    yField: 'accuracy',
    label: {
      position: 'middle' as const,
      style: {
        fill: '#FFFFFF',
        opacity: 0.6,
      },
    },
    meta: {
      accuracy: {
        alias: '准确率(%)',
      },
    },
    color: ['#1890ff', '#52c41a', '#fa8c16'],
  };

  // 时间趋势图表配置
  const timeSeriesConfig = {
    data: data?.time_series || [],
    xField: 'date',
    yField: 'accuracy_rate',
    point: {
      size: 5,
      shape: 'diamond',
    },
    label: {
      style: {
        fill: '#aaa',
      },
    },
    meta: {
      accuracy_rate: {
        alias: '准确率',
        formatter: (val: number) => `${(val * 100).toFixed(1)}%`,
      },
    },
    smooth: true,
  };

  // 雷达图配置
  const radarConfig = {
    data: data?.position_accuracy.map(item => ({
      name: item.position === 'hundreds' ? '百位' : 
            item.position === 'tens' ? '十位' : '个位',
      value: item.accuracy_rate * 100
    })) || [],
    xField: 'name',
    yField: 'value',
    meta: {
      value: {
        alias: '准确率(%)',
        min: 0,
        max: 100,
      },
    },
    xAxis: {
      line: null,
      tickLine: null,
    },
    yAxis: {
      label: false,
      grid: {
        alternateColor: 'rgba(0, 0, 0, 0.04)',
      },
    },
    point: {
      size: 2,
    },
    area: {},
  };

  // 热力图配置
  const heatmapConfig = {
    data: data?.heatmap_data.map(item => ({
      date: item.date,
      position: item.position === 'hundreds' ? '百位' : 
                item.position === 'tens' ? '十位' : '个位',
      value: item.accuracy_rate
    })) || [],
    xField: 'date',
    yField: 'position',
    colorField: 'value',
    color: ['#BAE7FF', '#1890FF', '#0050B3'],
    meta: {
      value: {
        alias: '准确率',
        formatter: (val: number) => `${(val * 100).toFixed(1)}%`,
      },
    },
  };

  if (error) {
    return (
      <Alert
        message="数据加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={fetchAnalyticsData}>
            重试
          </Button>
        }
      />
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 筛选控制面板 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <RangePicker
              value={filters.dateRange}
              onChange={(dates) => {
                if (dates) {
                  setFilters(prev => ({ ...prev, dateRange: dates as [dayjs.Dayjs, dayjs.Dayjs] }));
                }
              }}
              placeholder={['开始日期', '结束日期']}
            />
          </Col>
          <Col span={4}>
            <Select
              mode="multiple"
              placeholder="选择模型"
              value={filters.models}
              onChange={(value) => setFilters(prev => ({ ...prev, models: value }))}
              style={{ width: '100%' }}
            >
              <Option value="xgboost">XGBoost</Option>
              <Option value="lightgbm">LightGBM</Option>
              <Option value="lstm">LSTM</Option>
              <Option value="ensemble">集成模型</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              mode="multiple"
              placeholder="选择位置"
              value={filters.positions}
              onChange={(value) => setFilters(prev => ({ ...prev, positions: value }))}
              style={{ width: '100%' }}
            >
              <Option value="hundreds">百位</Option>
              <Option value="tens">十位</Option>
              <Option value="units">个位</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Radio.Group
              value={filters.timeGranularity}
              onChange={(e) => setFilters(prev => ({ ...prev, timeGranularity: e.target.value }))}
            >
              <Radio.Button value="daily">日</Radio.Button>
              <Radio.Button value="weekly">周</Radio.Button>
              <Radio.Button value="monthly">月</Radio.Button>
            </Radio.Group>
          </Col>
          <Col span={6}>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchAnalyticsData}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExport}
              >
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 统计摘要 */}
      {data && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="整体准确率"
                value={data.summary_stats.overall_accuracy * 100}
                precision={1}
                suffix="%"
                valueStyle={{ color: data.summary_stats.overall_accuracy > 0.4 ? '#3f8600' : '#cf1322' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总预测次数"
                value={data.summary_stats.total_predictions}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="最佳位置"
                value={data.summary_stats.best_position === 'hundreds' ? '百位' : 
                       data.summary_stats.best_position === 'tens' ? '十位' : '个位'}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="最佳模型"
                value={data.summary_stats.best_model}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 图表展示 */}
      <Spin spinning={loading}>
        <Tabs defaultActiveKey="position" type="card">
          <TabPane tab={<span><BarChartOutlined />位置分析</span>} key="position">
            <Row gutter={16}>
              <Col span={12}>
                <Card title="位置准确率对比" style={{ height: 400 }}>
                  {data && <Column {...positionChartConfig} height={300} />}
                </Card>
              </Col>
              <Col span={12}>
                <Card title="位置准确率雷达图" style={{ height: 400 }}>
                  {data && <Radar {...radarConfig} height={300} />}
                </Card>
              </Col>
            </Row>
          </TabPane>
          
          <TabPane tab={<span><LineChartOutlined />趋势分析</span>} key="trend">
            <Card title="准确率时间趋势" style={{ height: 400 }}>
              {data && <Line {...timeSeriesConfig} height={300} />}
            </Card>
          </TabPane>
          
          <TabPane tab={<span><HeatMapOutlined />热力图</span>} key="heatmap">
            <Card title="准确率热力图" style={{ height: 400 }}>
              {data && <Heatmap {...heatmapConfig} height={300} />}
            </Card>
          </TabPane>
        </Tabs>
      </Spin>
    </div>
  );
};

export default AccuracyAnalytics;
