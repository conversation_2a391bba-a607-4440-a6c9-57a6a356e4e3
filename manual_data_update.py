#!/usr/bin/env python3
"""
手动数据更新脚本
基于已知的开奖结果手动更新数据库
"""

import sqlite3
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def manual_update_database():
    """手动更新数据库"""
    # 基于数据源的最新数据
    new_records = [
        {'issue': '2025211', 'draw_date': '2025-08-09', 'hundreds': 8, 'tens': 9, 'units': 7},
        {'issue': '2025212', 'draw_date': '2025-08-10', 'hundreds': 4, 'tens': 5, 'units': 6},
    ]
    
    try:
        # 更新lottery.db
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        logger.info("🔄 更新lottery.db数据库")
        
        updated_count = 0
        for record in new_records:
            # 检查是否已存在
            cursor.execute("SELECT COUNT(*) FROM lottery_data WHERE issue = ?", (record['issue'],))
            exists = cursor.fetchone()[0] > 0
            
            if not exists:
                # 插入新记录
                cursor.execute("""
                    INSERT INTO lottery_data (
                        issue, draw_date, hundreds, tens, units,
                        sum_value, span_value, form_type, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record['issue'],
                    record['draw_date'],
                    record['hundreds'],
                    record['tens'],
                    record['units'],
                    record['hundreds'] + record['tens'] + record['units'],  # sum_value
                    max(record['hundreds'], record['tens'], record['units']) - min(record['hundreds'], record['tens'], record['units']),  # span_value
                    '组六' if len(set([record['hundreds'], record['tens'], record['units']])) == 3 else '组三',  # form_type
                    datetime.now().isoformat()
                ))
                updated_count += 1
                logger.info(f"✅ 插入新记录: 期号{record['issue']}, 号码{record['hundreds']}{record['tens']}{record['units']}")
            else:
                logger.info(f"ℹ️  记录已存在: 期号{record['issue']}")
        
        conn.commit()
        conn.close()
        
        logger.info(f"📊 lottery.db更新完成，新增 {updated_count} 条记录")
        
        # 更新fucai3d.db
        logger.info("🔄 同步数据到fucai3d.db")
        sync_to_fucai3d_db()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库更新失败: {e}")
        return False

def sync_to_fucai3d_db():
    """同步数据到fucai3d.db"""
    try:
        # 从lottery.db获取最新数据
        source_conn = sqlite3.connect('data/lottery.db')
        source_cursor = source_conn.cursor()
        
        source_cursor.execute("""
            SELECT issue, draw_date, hundreds, tens, units 
            FROM lottery_data 
            WHERE issue IN ('2025211', '2025212')
            ORDER BY issue DESC
        """)
        source_data = source_cursor.fetchall()
        source_conn.close()
        
        if not source_data:
            logger.warning("⚠️  源数据库没有找到目标数据")
            return False
        
        # 更新fucai3d.db
        target_conn = sqlite3.connect('data/fucai3d.db')
        target_cursor = target_conn.cursor()
        
        # 确保表存在
        target_cursor.execute("""
            CREATE TABLE IF NOT EXISTS lottery_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                issue TEXT UNIQUE,
                draw_date TEXT,
                hundreds INTEGER,
                tens INTEGER,
                units INTEGER,
                sum_value INTEGER,
                span_value INTEGER,
                form_type TEXT,
                created_at TEXT
            )
        """)
        
        synced_count = 0
        for record in source_data:
            issue, draw_date, hundreds, tens, units = record
            
            # 检查是否已存在
            target_cursor.execute("SELECT COUNT(*) FROM lottery_data WHERE issue = ?", (issue,))
            exists = target_cursor.fetchone()[0] > 0
            
            if not exists:
                target_cursor.execute("""
                    INSERT INTO lottery_data (
                        issue, draw_date, hundreds, tens, units,
                        sum_value, span_value, form_type, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    issue, draw_date, hundreds, tens, units,
                    hundreds + tens + units,
                    max(hundreds, tens, units) - min(hundreds, tens, units),
                    '组六' if len(set([hundreds, tens, units])) == 3 else '组三',
                    datetime.now().isoformat()
                ))
                synced_count += 1
                logger.info(f"✅ 同步到fucai3d.db: 期号{issue}, 号码{hundreds}{tens}{units}")
            else:
                # 更新现有记录
                target_cursor.execute("""
                    UPDATE lottery_data 
                    SET draw_date=?, hundreds=?, tens=?, units=?, 
                        sum_value=?, span_value=?, form_type=?
                    WHERE issue=?
                """, (
                    draw_date, hundreds, tens, units,
                    hundreds + tens + units,
                    max(hundreds, tens, units) - min(hundreds, tens, units),
                    '组六' if len(set([hundreds, tens, units])) == 3 else '组三',
                    issue
                ))
                logger.info(f"🔄 更新fucai3d.db: 期号{issue}, 号码{hundreds}{tens}{units}")
        
        target_conn.commit()
        target_conn.close()
        
        logger.info(f"📊 同步到fucai3d.db完成，处理 {len(source_data)} 条记录")
        return True
        
    except Exception as e:
        logger.error(f"❌ 同步到fucai3d.db失败: {e}")
        return False

def fix_prediction_issue():
    """修复预测期号错误"""
    try:
        conn = sqlite3.connect('data/fucai3d.db')
        cursor = conn.cursor()
        
        # 检查当前预测的期号
        cursor.execute("SELECT DISTINCT issue FROM final_predictions ORDER BY issue DESC LIMIT 1")
        result = cursor.fetchone()
        
        if result:
            current_issue = result[0]
            logger.info(f"📋 当前预测期号: {current_issue}")
            
            # 如果期号错误，需要更新
            if current_issue != '2025213':
                logger.info(f"🔧 修复预测期号: {current_issue} -> 2025213")
                cursor.execute("UPDATE final_predictions SET issue = '2025213' WHERE issue = ?", (current_issue,))
                conn.commit()
                logger.info("✅ 预测期号修复完成")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ 修复预测期号失败: {e}")
        return False

def verify_data():
    """验证数据更新结果"""
    try:
        # 验证lottery.db
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        cursor.execute("SELECT issue, draw_date, hundreds, tens, units FROM lottery_data ORDER BY draw_date DESC, issue DESC LIMIT 5")
        results = cursor.fetchall()
        conn.close()
        
        logger.info("🎯 lottery.db最新5条记录:")
        for r in results:
            logger.info(f"  期号:{r[0]}, 日期:{r[1]}, 号码:{r[2]}{r[3]}{r[4]}")
        
        # 验证fucai3d.db
        conn = sqlite3.connect('data/fucai3d.db')
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT issue FROM final_predictions ORDER BY issue DESC LIMIT 1")
        result = cursor.fetchone()
        if result:
            logger.info(f"🎯 fucai3d.db当前预测期号: {result[0]}")
        conn.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始手动数据更新修复")
    
    # 1. 手动更新数据库
    logger.info("📥 步骤1: 手动更新开奖数据")
    if not manual_update_database():
        logger.error("❌ 数据更新失败")
        return False
    
    # 2. 修复预测期号
    logger.info("🔧 步骤2: 修复预测期号")
    if not fix_prediction_issue():
        logger.error("❌ 预测期号修复失败")
        return False
    
    # 3. 验证结果
    logger.info("✅ 步骤3: 验证更新结果")
    verify_data()
    
    logger.info("🎉 手动数据更新修复完成")
    logger.info("📊 现在应该预测2025213期的号码")
    return True

if __name__ == "__main__":
    main()
