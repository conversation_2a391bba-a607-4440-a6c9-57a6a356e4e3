#!/usr/bin/env python3
"""紧急修复脚本 - 直接执行数据库操作"""

import sqlite3
import os
from datetime import datetime

def emergency_fix():
    """紧急修复数据同步问题"""
    print("🚨 紧急修复开始...")
    
    # 确保数据目录存在
    if not os.path.exists('data'):
        os.makedirs('data')
        print("📁 创建data目录")
    
    success_count = 0
    
    # 修复lottery.db
    try:
        print("📊 修复lottery.db...")
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        # 确保表存在
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS lottery_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                issue TEXT UNIQUE,
                draw_date TEXT,
                hundreds INTEGER,
                tens INTEGER,
                units INTEGER,
                sum_value INTEGER,
                span_value INTEGER,
                form_type TEXT,
                created_at TEXT
            )
        """)
        
        # 插入缺失数据
        data_to_insert = [
            ('2025211', '2025-08-09', 8, 9, 7, 24, 2, '组六'),
            ('2025212', '2025-08-10', 4, 5, 6, 15, 2, '组六')
        ]
        
        for issue, date, h, t, u, sum_val, span_val, form_type in data_to_insert:
            cursor.execute("""
                INSERT OR REPLACE INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, sum_value, span_value, form_type, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (issue, date, h, t, u, sum_val, span_val, form_type, datetime.now().isoformat()))
            print(f"  ✅ 插入期号{issue}: {h}{t}{u}")
        
        conn.commit()
        conn.close()
        success_count += 1
        print("✅ lottery.db修复完成")
        
    except Exception as e:
        print(f"❌ lottery.db修复失败: {e}")
    
    # 修复fucai3d.db
    try:
        print("📊 修复fucai3d.db...")
        conn = sqlite3.connect('data/fucai3d.db')
        cursor = conn.cursor()
        
        # 确保lottery_data表存在
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS lottery_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                issue TEXT UNIQUE,
                draw_date TEXT,
                hundreds INTEGER,
                tens INTEGER,
                units INTEGER,
                sum_value INTEGER,
                span_value INTEGER,
                form_type TEXT,
                created_at TEXT
            )
        """)
        
        # 确保final_predictions表存在
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS final_predictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                issue TEXT,
                hundreds INTEGER,
                tens INTEGER,
                units INTEGER,
                created_at TEXT
            )
        """)
        
        # 插入开奖数据
        for issue, date, h, t, u, sum_val, span_val, form_type in data_to_insert:
            cursor.execute("""
                INSERT OR REPLACE INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, sum_value, span_value, form_type, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (issue, date, h, t, u, sum_val, span_val, form_type, datetime.now().isoformat()))
        
        # 修复预测期号
        cursor.execute("UPDATE final_predictions SET issue = '2025213' WHERE issue != '2025213'")
        updated_rows = cursor.rowcount
        
        conn.commit()
        conn.close()
        success_count += 1
        print(f"✅ fucai3d.db修复完成，更新了{updated_rows}条预测记录")
        
    except Exception as e:
        print(f"❌ fucai3d.db修复失败: {e}")
    
    # 验证修复结果
    print("\n🔍 验证修复结果:")
    try:
        # 验证lottery.db
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        cursor.execute("SELECT issue, draw_date, hundreds, tens, units FROM lottery_data ORDER BY issue DESC LIMIT 3")
        results = cursor.fetchall()
        conn.close()
        
        print("📊 lottery.db最新记录:")
        for r in results:
            print(f"  期号:{r[0]}, 日期:{r[1]}, 号码:{r[2]}{r[3]}{r[4]}")
        
        # 验证fucai3d.db
        conn = sqlite3.connect('data/fucai3d.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT DISTINCT issue FROM final_predictions ORDER BY issue DESC LIMIT 1")
        result = cursor.fetchone()
        if result:
            print(f"🎯 当前预测期号: {result[0]}")
        
        cursor.execute("SELECT issue, hundreds, tens, units FROM lottery_data ORDER BY issue DESC LIMIT 2")
        lottery_results = cursor.fetchall()
        print("📊 fucai3d.db开奖记录:")
        for r in lottery_results:
            print(f"  期号:{r[0]}, 号码:{r[1]}{r[2]}{r[3]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
    
    if success_count == 2:
        print("\n🎉 紧急修复成功完成！")
        print("✅ 2025212期数据已更新 (开奖号码: 456)")
        print("✅ 预测期号已修正为: 2025213期")
        print("✅ 数据库同步完成")
        print("🚀 现在可以正确预测2025213期号码了！")
        return True
    else:
        print(f"\n⚠️  部分修复失败，成功{success_count}/2个数据库")
        return False

if __name__ == "__main__":
    emergency_fix()
